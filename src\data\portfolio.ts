// Portfolio data structure
export interface Contact {
    type: string;
    value: string;
}
export interface PersonalInfo {
    name: string;
    jobTitle: string;
    contacts: Contact[];
    profileImage: string;
}

export interface ResearchField {
    name: string;
}

export interface Publication {
    id: number;
    authors: string;
    year: number;
    title: string;
    journal: string;
    notes: string;
    thumbnail: string;
    type: "published" | "unpublished";
    // Enhanced fields for rich formatting
    authorsHtml?: string; // Optional HTML version of authors
    titleHtml?: string; // Optional HTML version of title
    journalHtml?: string; // Optional HTML version of journal
    notesHtml?: string; // Optional HTML version of notes
    furtherInfoLink?: string; // Link to more information
    furtherInfoText?: string; // Custom text for the link
}

export interface Experience {
    title: string;
    organization: string;
    period: string;
    responsibilities: string[];
}

export interface Education {
    degree: string;
    institution: string;
    period: string;
    details?: string[];
}

export interface Certification {
    title: string;
    issuer: string;
    issued: string;
}

export interface SoftwareScreen {
    name: string;
    description: string;
    image: string;
    alt: string;
}

export interface NavigationItem {
    href: string;
    label: string;
}

// Portfolio data
export const personalInfo: PersonalInfo = {
    name: "<PERSON><PERSON><PERSON>",
    jobTitle: "[Current Job Title – e.g., Data Scientist, Research Fellow]",
    contacts: [
        { type: "Email", value: "<EMAIL>" },
        { type: "Phone", value: "[+84 xxx xxx xxx]" },
        { type: "Address", value: "[City, Country] • [Institution or Organization]" },
    ],
    profileImage: "@/assets/images/profile-placeholder.svg",
};

export const researchFields: ResearchField[] = [
    { name: "Causal Inference" },
    { name: "Machine Learning" },
    { name: "Econometrics" },
    { name: "Bayesian Modeling" },
    { name: "Policy Evaluation" },
    { name: "R / Stata / Python" },
];

let pubId = 0;
export const publications: Publication[] = [
    {
        id: pubId++,
        authors: "Nguyen Cong Thanh, A. Researcher, B. Analyst",
        year: 2024,
        title: "Causal Effects of Program Participation: A Bayesian Hierarchical Approach",
        journal: "Journal of Statistical Research",
        notes: "Open data and code available upon request.",
        thumbnail: "@/assets/images/publication-thumb.svg",
        type: "published",
        authorsHtml: "<strong>Nguyen Cong Thanh</strong>, A. Researcher, B. Analyst",
        titleHtml: "<em>Causal Effects of Program Participation: A Bayesian Hierarchical Approach</em>",
        journalHtml: "<strong>Journal of Statistical Research</strong>",
        notesHtml: "<em>Open data and code available upon request.</em>",
        furtherInfoLink: "#",
        furtherInfoText: "further information",
    },
    {
        id: 2,
        authors: "Nguyen Cong Thanh, C. Scientist",
        year: 2023,
        title: "Instrumental Variables in High-Dimensional Settings",
        journal: "Econometrics Letters",
        notes: "Open data and code available upon request.",
        thumbnail: "@/assets/images/publication-thumb.svg",
        type: "published",
        authorsHtml: "<strong>Nguyen Cong Thanh</strong>, C. Scientist",
        titleHtml: "<em>Instrumental Variables in High-Dimensional Settings</em>",
        journalHtml: "<strong>Econometrics Letters</strong>",
        notesHtml: "<em>Open data and code available upon request.</em>",
        furtherInfoLink: "#",
        furtherInfoText: "further information",
    },
    {
        id: 3,
        authors: "Nguyen Cong Thanh, D. Coauthor",
        year: 2022,
        title: "Robust Policy Evaluation with Doubly Robust Estimators",
        journal: "Applied Data Science Journal",
        notes: "Featured at the 2022 Data Science Symposium.",
        thumbnail: "@/assets/images/publication-thumb.svg",
        type: "published",
        authorsHtml: "<strong>Nguyen Cong Thanh</strong>, D. Coauthor",
        titleHtml: "<em>Robust Policy Evaluation with Doubly Robust Estimators</em>",
        journalHtml: "<strong>Applied Data Science Journal</strong>",
        notesHtml: "<em>Featured at the 2022 Data Science Symposium.</em>",
        furtherInfoLink: "#",
        furtherInfoText: "further information",
    },
    {
        id: 4,
        authors: "Nguyen Cong Thanh",
        year: 2025,
        title: "Synthetic Controls with Bayesian Regularization",
        journal: "Manuscript under review",
        notes: "Preprint available upon request.",
        thumbnail: "@/assets/images/publication-thumb.svg",
        type: "unpublished",
        authorsHtml: "<strong>Nguyen Cong Thanh</strong>",
        titleHtml: "<em>Synthetic Controls with Bayesian Regularization</em>",
        journalHtml: "<strong>Manuscript under review</strong>",
        notesHtml: "<em>Preprint available upon request.</em>",
        furtherInfoLink: "#",
        furtherInfoText: "further information",
    },
    {
        id: 5,
        authors: "Nguyen Cong Thanh, E. Collaborator",
        year: 2024,
        title: "Heterogeneous Treatment Effects with Partial Identification",
        journal: "Working paper",
        notes: "Preprint available upon request.",
        thumbnail: "@/assets/images/publication-thumb.svg",
        type: "unpublished",
        authorsHtml: "<strong>Nguyen Cong Thanh</strong>, E. Collaborator",
        titleHtml: "<em>Heterogeneous Treatment Effects with Partial Identification</em>",
        journalHtml: "<strong>Working paper</strong>",
        notesHtml: "<em>Preprint available upon request.</em>",
        furtherInfoLink: "#",
        furtherInfoText: "further information",
    },
];

export const employment: Experience[] = [
    {
        title: "Data Scientist",
        organization: "[Company / Lab Name]",
        period: "2023–Present",
        responsibilities: [
            "Lead econometric modeling and causal inference for policy analysis.",
            "Designed end-to-end ML pipelines in Python and Stata.",
            "Collaborated with academia and stakeholders to translate findings.",
        ],
    },
    {
        title: "Research Assistant",
        organization: "[University / Institute]",
        period: "2021–2023",
        responsibilities: [
            "Implemented Bayesian hierarchical models for panel data.",
            "Co-authored publications and conference presentations.",
        ],
    },
];

export const education: Education[] = [
    {
        degree: "Ph.D., Econometrics / Data Science",
        institution: "[University Name]",
        period: "2018–2022",
        details: [
            "Dissertation: Robust Methods for Causal Effect Estimation.",
            "Teaching: Statistical Inference, Econometrics I.",
        ],
    },
    {
        degree: "M.Sc., Statistics",
        institution: "[University Name]",
        period: "2016–2018",
    },
    {
        degree: "B.Sc., Mathematics",
        institution: "[University Name]",
        period: "2012–2016",
    },
];

export const certifications: Certification[] = [
    {
        title: "Certified Data Scientist",
        issuer: "[Issuing Organization]",
        issued: "Issued 2024",
    },
];

export const softwareScreens: SoftwareScreen[] = [
    {
        name: "Stata",
        description: "Stata interface placeholder",
        image: "@/assets/images/software-screenshot.svg",
        alt: "Placeholder for Stata interface screenshot",
    },
    {
        name: "SPSS",
        description: "SPSS interface placeholder",
        image: "@/assets/images/software-screenshot.svg",
        alt: "Placeholder for SPSS interface screenshot",
    },
];

export const navigation: NavigationItem[] = [
    { href: "#profile", label: "Profile" },
    { href: "#publications", label: "Publications" },
    { href: "#experience", label: "Experience" },
    { href: "#screens", label: "Software Screens" },
];

export const siteInfo = {
    title: "Nguyen Cong Thanh // Portfolio",
    brandName: "N. C. Thanh // Portfolio",
    skipToContentText: "Skip to content",
    footerText: "Academic Portfolio",
};
