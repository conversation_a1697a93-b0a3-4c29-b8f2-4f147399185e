---
import { Picture } from "astro:assets";
import type { PersonalInfo, ResearchField } from "@/data/portfolio";
import profilePlaceholder from "@/assets/images/profile-placeholder.svg";

interface Props {
    personalInfo: PersonalInfo;
    researchFields: ResearchField[];
}

const { personalInfo, researchFields } = Astro.props;
---

<section id="profile" class="pt-10 sm:pt-14 pb-10">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 items-center">
        <!-- Right Column: Profile Information -->
        <div class="order-2">
            <h1 class="text-3xl sm:text-4xl lg:text-5xl text-sky-900 font-extrabold leading-tight">
                {personalInfo.name}
            </h1>
            <p class="mt-2 text-lg text-slate-600 italic">{personalInfo.jobTitle}</p>

            <dl class="mt-6 grid grid-cols-1 gap-3" aria-label="Professional contact information">
                {
                    personalInfo.contacts.map((contact) => (
                        <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
                            <dt class="sr-only">{contact.type}</dt>
                            <dd class="text-sm font-medium">
                                {contact.type}: {contact.value}
                            </dd>
                        </div>
                    ))
                }
            </dl>

            <div class="mt-6">
                <h2 class="text-xl text-sky-900 font-bold">Research Fields</h2>
                <ul class="mt-3 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
                    {
                        researchFields.map((field: ResearchField) => (
                            <li>
                                <span class="inline-block bg-sky-50 text-sky-900 border border-sky-200 px-4 py-2 rounded-full text-sm font-medium hover:bg-sky-100 transition-colors">
                                    {field.name}
                                </span>
                            </li>
                        ))
                    }
                </ul>
            </div>
        </div>

        <!-- Left Column: Large Hero Profile Image -->
        <div class="order-1">
            <figure class="bg-transparent grid place-items-center shadow-xl max-w-80 md:max-w-[400px] mx-auto">
                <Picture
                    src={profilePlaceholder}
                    alt={`Profile picture placeholder for ${personalInfo.name}`}
                    width={400}
                    height={400}
                    loading="eager"
                    fetchpriority="high"
                />
            </figure>
        </div>
    </div>
</section>
